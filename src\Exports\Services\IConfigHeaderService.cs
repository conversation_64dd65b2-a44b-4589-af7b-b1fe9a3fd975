using System.Collections.Generic;
using System.Threading.Tasks;
using Contract.CapTable;
using DataAccessLayer.Models.PageSettings;

namespace Exports.Services
{
    public interface IConfigHeaderService
    {
        Task<string> CreateKpiExcelHeaders(string moduleName, string destinationTemplate,int companyId);
        Task<string> CreateFinancialsExcelTemplate(string destinationTemplate, int companyId, int userId);
        string GetPageConfigName(string pageConfigName);
        Task<string> CreateCapTableExcelTemplate(string destinationTemplate, int companyId, int userId, bool isOtherCapTable = false);
        List<CapTemplateModule> GetActiveCapTableFieldNames(int subPageId);
        Task<List<MSubSectionFields>> GetCapTableSections(int subPageId);
        Task<string> CreateFundFinancialsAndKpisExcelTemplate(string destinationTemplate, int? fundId, int userId, string sectionName, string fundName);
    }
}